package com.neijiang.mobile.service;

import com.neijiang.mobile.model.User;
import java.util.*;

/**
 * 模拟用户服务 - 用于演示和测试
 * 使用内存数据，不依赖数据库
 */
public class MockUserService {
    private static Map<String, User> users = new HashMap<>();
    
    static {
        // 初始化一些测试用户
        User admin = new User();
        admin.setId(1);
        admin.setUsername("admin");
        admin.setPassword("admin123");
        admin.setRealName("系统管理员");
        admin.setEmail("<EMAIL>");
        admin.setPhone("13800138000");
        admin.setDepartment("技术部");
        admin.setPosition("系统管理员");
        admin.setRoleId(1);
        admin.setStatus(1);
        admin.setCreateTime(new Date());
        admin.setUpdateTime(new Date());
        users.put("admin", admin);
        
        User user = new User();
        user.setId(2);
        user.setUsername("user");
        user.setPassword("user123");
        user.setRealName("普通用户");
        user.setEmail("<EMAIL>");
        user.setPhone("13800138001");
        user.setDepartment("业务部");
        user.setPosition("业务员");
        user.setRoleId(2);
        user.setStatus(1);
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        users.put("user", user);
    }
    
    /**
     * 用户登录
     */
    public User login(String username, String password) {
        User user = users.get(username);
        if (user != null && user.getPassword().equals(password) && user.getStatus() == 1) {
            return user;
        }
        return null;
    }
    
    /**
     * 获取所有用户
     */
    public List<User> getAllUsers() {
        return new ArrayList<>(users.values());
    }
    
    /**
     * 根据ID获取用户
     */
    public User getUserById(int id) {
        for (User user : users.values()) {
            if (user.getId() == id) {
                return user;
            }
        }
        return null;
    }
    
    /**
     * 根据用户名获取用户
     */
    public User getUserByUsername(String username) {
        return users.get(username);
    }
    
    /**
     * 添加用户
     */
    public boolean addUser(User user) {
        if (users.containsKey(user.getUsername())) {
            return false; // 用户名已存在
        }
        user.setId(users.size() + 1);
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        users.put(user.getUsername(), user);
        return true;
    }
    
    /**
     * 更新用户
     */
    public boolean updateUser(User user) {
        if (users.containsKey(user.getUsername())) {
            user.setUpdateTime(new Date());
            users.put(user.getUsername(), user);
            return true;
        }
        return false;
    }
    
    /**
     * 删除用户
     */
    public boolean deleteUser(String username) {
        return users.remove(username) != null;
    }
    
    /**
     * 修改密码
     */
    public boolean changePassword(String username, String oldPassword, String newPassword) {
        User user = users.get(username);
        if (user != null && user.getPassword().equals(oldPassword)) {
            user.setPassword(newPassword);
            user.setUpdateTime(new Date());
            return true;
        }
        return false;
    }
}
